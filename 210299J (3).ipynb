#%% md
# <center>Feedforward and Backpropagation</center>
## <center>Inclass Project 2 - MA4144</center>

This project contains 12 tasks/questions to be completed, some require written answers. Questions that required written answers are given in blue fonts. Some written questions are open ended, they do not have a correct or wrong answer. You are free to give your opinions, but please provide related answers within the context.

After finishing project run the entire notebook once and **save the notebook as a pdf** (File menu -> Save and Export Notebook As -> PDF). You are **required to upload this PDF on moodle and also the ipynb notebook file as well**.

***
#%% md
## Outline of the project

The aim of the project is to build a Multi Layer perceptron (MLP) model from scratch for binary classification. That is given an input $x$ output the associated class label $0$ or $1$.

In particular, we will classify images of handwritten digits ($0, 1, 2, \cdots, 9$). For example, given a set of handwritten digit images that only contain two digits (Eg: $1$ and $5$) the model will classify the images based on the written digit.

For this we will use the MNIST dataset (collection of $28 \times 28$ images of handwritten digits) - you can find additional information about MNIST [here](https://en.wikipedia.org/wiki/MNIST_database).

<img src="https://upload.wikimedia.org/wikipedia/commons/f/f7/MnistExamplesModified.png" width="250">

***

#%% md
Use the below cell to use any include any imports
#%%
! pip install tensorflow
#%%
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score
import keras
#%% md
## Section 1: Preparing the data
#%%
#Load the dataset as training and testing, then print out the shapes of the data matrices.
#The training data will be provided to you.

# data = np.load('/kaggle/input/mnist-data/train_mnist.npz')
# train_X = data['x']
# train_y = data['y']
# print(train_X.shape)
# print(train_y.shape)
(train_X, train_y), (test_X, test_y) = keras.datasets.mnist.load_data()
#%% md
**Q1.** In the following cell write code to display $5$ random images in train_X and it's corresponding label in train_y. Each time it is run, you should get a different set of images. The imshow function in the matplotlib library could be useful. Display them as [grayscale images](https://en.wikipedia.org/wiki/Grayscale).
#%%
fig, axes = plt.subplots(1, 5, figsize=(15, 3))
random_indices = np.random.choice(train_X.shape[0], 5, replace=False)
for i, ax in enumerate(zip(axes, random_indices)):
    image, label = train_X[ax[1]], train_y[ax[1]]
    ax[0].imshow(image.reshape(28, 28), cmap='gray')
    ax[0].set_title(f"Label: {label}")
    ax[0].axis('off')
#%% md
**Q2.** Given two digits $d_1$ and $d_2$, both between $0$ and $9$, in the following cell fill in the function body to extract all the samples corresponding to $d_1$ or $d_2$ only, from the dataset $X$ and labels $y$. You can use the labels $y$ to filter the dataset. Assume that the label for the $i$th image $X[i]$ in $X$ is given by $y[i]$. The function should return the extracted samples $X_{extracted}$ and corresponding labels $y_{extracted}$. Avoid using for loops as much as possible, infact you do not need any for loops. numpy.where function should be useful.
#%%
def extract_digits(X, y, d1, d2):

    assert d1 in range(0, 10), "d1 should be a number between 0 and 9 inclusive"
    assert d2 in range(0, 10), "d2 should be a number between 0 and 9 inclusive"
    
    indices = np.where((y == d1) | (y == d2))
    X_extracted = X[indices]
    y_extracted = y[indices]
    return (X_extracted, y_extracted)
#%%
# plt.imshow(X_train_extracted[0].reshape(28, 28), cmap='gray')
# plt.axis('off')
# plt.show()
# print(y_train_extracted[0])
#%% md
**Q3.** Both the training dataset is a 3 dimensional numpy array, each image occupies 2 dimensions. For convenience of processing data we usually comvert each $28 \times 28$ image matrix to a vector with $784$ entries. We call this process **vectorize images**.

Once we vectorize the images, the vectorized data set would be structured as follows: $i$th row will correspond to a single image and $j$th column will correspond to the $j$th pixel value of each vectorized image. However going along with the convention we discussed in the lecture, the input to the MLP model will require that the columns correspond to individual images. Hence we also require a transpose of the vectorized results.

The pixel values in the images will range from $0$ to $255$. Normalize the pixel values between $0$ and $1$, by dividing each pixel value of each image by the maximum pixel value of that image. Simply divide each column of the resulting matrix above by the max of each column. 

<center><img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTdN_8m9FEqjqAB07obTmB6gNc7S2rSoGBYaA&s"></center>

Given a dataset $X$ of size $N \times 28 \times 28$, in the following cell fill in the function to do the following in order;
1. Vectorize the dataset resulting in dataset of size $N \times 784$.
2. Transpose the vectorized result.
3. Normalize the pixel values of each image.
4. Finally return the vectorized, transposed and normalized dataset $X_{transformed}$.

Again, avoid for loops, functions such as numpy.reshape, numpy.max etc should be useful.
#%%
def vectorize_images(X):
    X_vectorized=X.reshape(-1,28*28)
    X_transposed=X_vectorized.transpose()
    X_normalized=X_transposed/255

    return(X_normalized)
#%% md
**Q4.** In the following cell write code to;

1. Extract images of the digits $d_1 = 1$ and $d_2 = 5$ with their corresponding labels for the training set (train_X, train_y).
2. Then vectorize the data, tranpose the result and normlize the images.
3. Store the results after the final transformations in numpy arrays train_X_1_5, train_y_1_5.
4. Our MLP will output only class labels $0$ and $1$ (not $1$ and $5$), so create numpy arrays to store the class labels as follows:
   $d_1 = 1$ -> class label = 0 and $d_2 = 5$ -> class label = 1. Store them in an array named train_y_1_5.

Use the above functions you implemented above to complete this task. In addtion, numpy.where could be useful. Avoid for loops as much as possible.
#%%
#TODO
#Extract and organize the training data as described above.
#Here you will be using the functions you implemented above appropriately
X_train_extracted,y_train_extracted=extract_digits(train_X, train_y, 1, 5)
X_test_extracted,y_test_extracted=extract_digits(test_X, test_y, 1, 5)

train_X_1_5 = vectorize_images(X_train_extracted)
test_X_1_5 = vectorize_images(X_test_extracted)

#%%
def change_labels(y,d1,d2):
    y[(y == d1)] = 0
    y[(y == d2)] = 1
    return y
#%%
train_y_1_5=change_labels(y_train_extracted,1,5)
test_y_1_5=change_labels(y_test_extracted,1,5)
#%% md
## Section 2: Implementing MLP from scratch with training algorithms.
#%% md
Now we will implement code to build a customizable MLP model. The hidden layers will have the **Relu activation function** and the final output layer will have **Sigmoid activation function**.
#%% md
**Q5.** Recall the following about the activation functions:
1. Sigmoid activation: $y = \sigma(z) = \frac{1}{1 + e^{-z}}$.
2. Derivative of Sigmoid: $y' = \sigma'(z) = \sigma(z) (1 - \sigma(z)) = y(1-y)$
3. ReLu activation: $y = ReLu(z) = max(0, z)$
4. Derivative of ReLu: $y' = ReLu'(z) = \begin{cases} 0 \; \textrm{if } z < 0 \\ 1 \; \textrm{otherwise} \end{cases} = \begin{cases} 0 \; \textrm{if } y = 0 \\ 1 \; \textrm{otherwise} \end{cases}$

In the following cell implement the functions to compute activation functions Sigmoid and ReLu given $z$ and derivatives of the Sigmoid and ReLu activation functions given $y$. Note that, in the implementation, the derivative functions should actually accept $y$ as the input not $z$.

In practice the input will not be just single numbers, but matrices. So functions or derivatives should be applied elementwise on matrices. Again avoid for loops, use the power of numpy arrays - search for numpy's capability of doing elementwise computations.

Important: When implementing the sigmoid function make sure you handle overflows due to $e^{-z}$ being too large. To avoid you can choose to set the sigmoid value to 'the certain appropriate value' if $z$ is less than a certain good enough negative threshold. If you do not handle overflows, the entire result will be useless since the MLP will just output Nan (not a number) for every input at the end.
#%%
def sigmoid(Z):
    Z=np.clip(Z,-50,50)
    sigma=1/(1+np.exp(-Z))
    return(sigma)

def deriv_sigmoid(Y):
    sigma_prime=Y*(1-Y)
    return(sigma_prime)
    
def ReLu(Z):
    relu = np.maximum(0, Z)
    return(relu)

def deriv_ReLu(Y):
    relu_prime = np.where(Y > 0, 1, 0)
    return(relu_prime)

#%% md
**Q6.** The following piece of code defines a simple MLP architecture as a Python class and subsequent initialization of a MLP model. <font color='blue'>Certain lines of code contains commented line numbers. Write a short sentence for each such line explaining its purpose. Feel free to refer to the lecture notes or any resources to answers these question. In addition, explain what the Y, Z, W variables refer to and their purpose</font>
#%%
# hidden_layers = [500, 250, 50]
# len(hidden_layers)
#%%
class NNet:
    def __init__(self, input_size = 784, output_size = 1, batch_size = 1000, hidden_layers = [500, 250, 50]):
        self.Y = []
        self.Z = []
        self.W = []
        self.input_size = input_size
        self.output_size = output_size
        self.batch_size = batch_size
        self.hidden_layers = hidden_layers

        layers = [input_size] + hidden_layers + [output_size]
        L = len(hidden_layers) + 1
    
        for i in range(1, L + 1):
            self.Y.append(np.zeros((layers[i], batch_size)))                        #line1
            self.Z.append(np.zeros((layers[i], batch_size)))                        #Line2
            self.W.append(2*(np.random.rand(layers[i], layers[i-1] + 1) - 0.5))     #Line3
#%% md
**Questions** (write answers in the cell below as strings)

(i) What does the Y, Z, W variables refer to and their purpose?

(ii) Line1: Explanation

(iii) Line2: Explanation

(iv) Line3: Explanation
#%%
#Write answers here as strings:

ans_i = "Y: post-activation outputs (activations) of each layer for the current batch, Z: pre-activation linear sums W·[prevY;1] for each layer, W: weight matrices (including an extra bias column) mapping from previous layer (+ bias) to the current layer"
ans_ii = "Pre-allocate a zero matrix to store the activations Y^(l) for layer i (shape: neurons in layer i × batch_size)"
ans_iii = "Pre-allocate a zero matrix to store the pre-activations Z^(l) for layer i, used before applying the nonlinearity"
ans_iv = "Initialize the weight matrix W^(l) with random values in [-1, 1), sized (neurons in current layer) × (neurons in previous layer + 1), where the extra +1 column is for the bias"
#%% md
**Q7.** Now we will implement the feedforward algorithm. Recall from the lectures that for each layer $l$ there is input $Y^{(l-1)}$ from the previous layer if $l > 1$ and input data $X$ if $l = 1$. Then we compute $Z^{(l)}$ using the weight matrix $W^{(l)}$ as follows from matrix multiplication:

$Z^{(l)} = W^{(l)} Y^{(l-1)}$

Make sure that during multiplication you add an additional row of one's to $Y^{(l-1)}$ to accommodate the bias term (concatenate the row of ones as the last row to be consistent with the grader). However, the rows of ones should not permanently remain on $Y^{(l-1)}$. <font color='blue'>Explain what the bias term is and how adding a row of one's help with the bias terms.</font> The weight matrices are initialised to afford this extra bias term, so no change to either $Z^{(l)}$ or $W^{(l)}$ is needed.

Next compute $Y^{(l)}$, the output of layer $l$ by activation through sigmoid.

$Y^{(l)} = \sigma(Z^{(l)})$

The implemented feedforward algorithm should take in a NNet model and an input matrix $X$ and output the modified MLP model - the $Y$'s and $Z$'s computed should be stored in the model for the backpropagation algorithm.

As usual, avoid for loops as much as possible, use the power of numpy. However, you may use a for loop to iterate through the layers of the model.
#%%
def feedforward(model, X):
    Y_prev = X

    L = len(model.hidden_layers)  # number of layers excluding input layer
    for l in range(L):
        # Append bias row of ones (do NOT store it in model.Y)
        Y_bias = np.vstack([Y_prev, np.ones((1, Y_prev.shape[1]))])  # (prev_dim+1, B)

        # Linear step
        Z = np.dot(model.W[l],Y_bias)
        model.Z[l] = Z

        # Nonlinearity: ReLU for hidden layers
        Y = ReLu(Z)
        model.Y[l] = Y 
        Y_prev = Y
        
    Y_bias = np.vstack([Y_prev, np.ones((1, Y_prev.shape[1]))]) 
    Z = np.dot(model.W[-1], Y_bias)
    model.Z[-1] = Z    
    Y = sigmoid(Z)
    model.Y[-1] = Y
    
    return model
#%% md
**Question** (write answer in the cell below as a string)

Explain what the bias term is and how adding a row of one's help with the bias terms.
#%%
#Write the answer here as a string:

ans_bias = "The bias term is an additional constant added to each neuron that allows the model to shift the activation function" \
"independently of the weighted sum of inputs. Without bias, a neuron’s output would always pass through the origin, limiting flexibility. " \
"By appending a row of ones to the previous layer’s output, the bias can be treated as just another weight (the last column of W) " \
"multiplied by 1. This way the bias is naturally included in the matrix multiplication step without needing special handling."
#%% md

#%% md
**Q8.** Now we will implement the backpropagation algorithm. The cost function $C$ at the end is given by the square loss.

$C = \frac{1}{2} ||Y^{(L)} - Y||^{2}$, where $Y^{(L)}$ is the final output vector of the feedforward algorithm and $Y$ is the actual label vector associated with the input $X$.

At each layer $l = 1, 2, \cdots, L$ we compute the following (note that the gradients are matrices with the same dimensions as the variable to which we derivating with respect to):

1. Gradient of $C$ with respect to $Z^{(l)}$ as <br> $\frac{\partial C}{\partial Z^{(l)}} = deriv(A^{(l)}(Z^{(l)})) \odot \frac{\partial C}{\partial Y^{(L)}} $, <br> where $A^{(l)}$ is the activation function of the $l$th layer, and we use the derivative of that here. The $\odot$ refers to the elementwise multiplication.

2. Gradient of $C$ with respect to $W^{(l)}$ as <br> $\frac{\partial C}{\partial W^{(l)}} = \frac{\partial C}{\partial Z^{(l)}} (Y^{(l-1)})^{T}$ <br> this is entirely matrix multiplication.

3. Gradient of $C$ with respect to $Y^{(l-1)}$ as <br> $\frac{\partial C}{\partial Y^{(l-1)}} = (W^{(l)})^{T} \frac{\partial C}{\partial Z^{(l)}}$ <br> this is also entirely matrix multiplication.

4. Update weights by: <br> $W^{(l)} \leftarrow W^{(l)} - \eta \frac{\partial C}{\partial W^{(l)}}$, <br> where $\eta > 0$ is the learning rate.

The loss derivative (the gradient of $C$ with respect to $Y^{(L)}$) at the last layer is given by:

$\frac{\partial C}{\partial Y^{(L)}} = Y^{(L)} - Y$

By convention we consider $Y^{(0)} = X$, the input data.

Based on the backpropagation algorithm implement the backpropagation method in the following cell. Remember to temporarily add a row of ones to $Y^{(l-1)}$ (as the last row to be consistent with the grader) when computing $\frac{\partial C}{\partial W^{(l)}}$ as we discussed back in the feedforward algorithm. Make sure you avoid for loops as much as possible.

The function takes in a NNet model, input data $X$ and the corresponding class labels $Y$. learning rate can be set as desired.
#%%
def backpropagation(model, X, Y, eta = 0.01):
    L = len(model.hidden_layers)  # number of layers

    # 1) Start from loss derivative wrt final activations
    dC_dY = model.Y[-1] - Y  # shape: (output_dim, B)

    # 2) Backprop through layers L-1 ... 0
    for l in reversed(range(L+1)):
        Y_l = model.Y[l]
       
        if l == L:  
            # Output layer using sigmoid
            dY_dZ = deriv_sigmoid(model.Y[l])
        else:  
            # Hidden layers using ReLU
            dY_dZ = deriv_ReLu(model.Y[l])
        
        dC_dZ = dY_dZ * dC_dY
        
        if l == 0:
            Y_prev_with_bias = np.vstack([X, np.ones((1, X.shape[1]))])
        else:
            Y_prev_with_bias = np.vstack([model.Y[l-1], np.ones((1, model.Y[l-1].shape[1]))])
        
        dC_dW = np.dot(dC_dZ, Y_prev_with_bias.T)
        model.W[l] -= eta * dC_dW

        # Compute the gradient of cost with respect to Y^(l-1) if l > 0
        if l > 0: dC_dY = np.dot(model.W[l].T, dC_dZ)[:-1, :]  # Skip the bias row in the backpropagation

    return model

#%% md
**Q9.** Now implement the training algorithm.

The training method takes in training data $X$, actual label $Y$, number of epochs, batch_size, learning rate $\eta > 0$. The training will happen in epochs. For each epoch, permute the data columns of both $X$ and $Y$, then divide both $X$ and $Y$ into mini batches each with the given batch size. Then run the feedforward and backpropagation for each such batch iteratively.

At the end of each iteration, keep trach of the cost $C$ and the $l_2$-norm of change in each weight matrix $W^{(l)}$.

At the end of the last epoch, plot the variation cost $C$ and change in weight matrices. Then return the trained model.


#%%
from sklearn.model_selection import train_test_split

def train_NNet(X, Y, epochs = 20, batch_size = 512, eta = 0.01):
    lr = 0.001
    batch_size = 512
    cost_history = [float('inf')]
    weight_changes_history = []
    
    model = NNet(input_size = 784, output_size = 1, batch_size = batch_size, hidden_layers = [512, 64, 32, 4])
    previous_weights = [np.copy(w) for w in model.W]
    X, X_val, Y, Y_val = train_test_split(X.T, Y, test_size=0.2, random_state=42)
    X = X.T
    X_val = X_val.T
    # Get number of samples
    num_samples = X.shape[1]

    # Training loop over epochs
    for epoch in range(epochs):

        batch_costs = []
        weight_changes = [0] * len(model.W)

        # Loop over mini-batches
        for i in range(0, num_samples, batch_size):
            # Create mini-batch for X and Y
            X_batch = X[:, i:i + batch_size]
            Y_batch = Y[i:i + batch_size]

            model = feedforward(model, X_batch)
            model = backpropagation(model, X_batch, Y_batch, lr)

            # Compute cost for the current mini-batch
            Y_pred = model.Y[-1]
            batch_cost = 0.5 * np.mean(np.square(Y_pred - Y_batch))
            batch_costs.append(batch_cost)

            # Compute weight changes (l2-norm of the difference)
            weight_changes = [np.linalg.norm(model.W[l] - previous_weights[l]) for l in range(len(model.W))]
            previous_weights = [np.copy(w) for w in model.W]

        # Average cost and weight change per epoch
        epoch_cost = np.mean(batch_costs)
        epoch_weight_change = [change / (num_samples // batch_size) for change in weight_changes]
        
        # Record cost and weight change for the epoch
        cost_history.append(epoch_cost)
        weight_changes_history.append(epoch_weight_change)
        a, b = predict(X_val, Y_val, model, 0, 1)

        # Print progress
        print(f"Epoch {epoch + 1}/{epochs}, Cost: {epoch_cost:.4f}", f"val: {a}")

    # # Plot the cost over epochs
    # plt.figure(figsize=(12, 5))
    # plt.subplot(1, 2, 1)
    # plt.plot(cost_history[1:], label='Cost')
    # plt.xlabel('Epochs')
    # plt.ylabel('Cost')
    # plt.title('Cost Over Epochs')
    # plt.legend()

    # # Plot the weight changes over epochs
    # plt.subplot(1, 2, 2)
    # for l in range(len(model.W)):
    #     plt.plot([change[l] for change in weight_changes_history], label=f'Layer {l+1}')
    # plt.xlabel('Epochs')
    # plt.ylabel('Weight Change (L2 Norm)')
    # plt.title('Weight Changes Over Epochs')
    # plt.legend()

    # plt.tight_layout()
    # plt.show()

    return model
#%% md
## Section 3: Evaluation using test data
#%% md
**Q10.** Implement the following function predict. Given test images (3 dimensional numpy array that contains $28 \times 28$ digit images) it will correctly recognize the written digits between d1 and d2. You can assume that test_images will only contain images of digits d1 and d2. Inside predict you would need to preprocess the images using vectorize, predict the correct labels using model and then output the correct lables. Output should be a vector predicted of d1's and d2's, predicted[i] should correspond to the test_image[i].
#%%
def predict(test_data, test_labels, model, d1, d2):
    
    L = len(model.hidden_layers) + 1
    
    Y = test_data
    for i in range(L):
        Z = np.matmul(model.W[i], np.append(Y, np.array([np.ones(Y.shape[1])]), axis = 0))
        if i < L - 1:
            Y = ReLu(Z)
        else:
            Y = sigmoid(Z)
    
    Y = Y[0]
    Y = np.where(Y >= 0.5, 1, 0)
    Y_predicted = np.where(Y == 0, d1, d2)

    acc = accuracy_score(test_labels, Y_predicted)

    return(acc, Y_predicted)
#%% md
**Q11.** Use the train_NNet function to train a MLP model to classify between images of digits $1$ and $5$. An accuracy $>= 99%$ is achievable. Test with different batch sizes, $\eta$ values and hidden layers. Find which of those hyperparameters gives the best test accuracy. Name this model, model_1_5. This model will be tested on unseen test data within the grader. So make sure you train the best possible model. The grader will use your own predict function to evaluate the model.
#%%
d1, d2 = 1, 5

train_X_d1_d2, train_y_d1_d2 = extract_digits(train_X, train_y, d1, d2)
test_X_d1_d2, test_y_d1_d2 = extract_digits(test_X, test_y, d1, d2)

train_X_d1_d2 = vectorize_images(train_X_d1_d2)
test_X_d1_d2 = vectorize_images(test_X_d1_d2)

train_y_label_d1_d2 = change_labels(train_y_d1_d2, d1, d2)

model_1_5 = train_NNet(train_X_d1_d2, train_y_label_d1_d2)
acc, Y_predicted = predict(test_X_d1_d2, test_y_d1_d2, model_1_5, d1, d2)
acc
#%% md
**Q12.** Do the same as in Q11 with the digits $7$ and $9$ Name this model, model_7_9. This model will be tested on unseen test data within the grader. So make sure you train the best possible model. The grader will use your own predict function to evaluate the model.
#%%
d1, d2 = 7,9

train_X_d1_d2, train_y_d1_d2 = extract_digits(train_X, train_y, 7, 9)
test_X_d1_d2, test_y_d1_d2 = extract_digits(test_X, test_y, 7, 9)

train_X_d1_d2 = vectorize_images(train_X_d1_d2)
test_X_d1_d2 = vectorize_images(test_X_d1_d2)

train_y_label_d1_d2 = change_labels(train_y_d1_d2, d1, d2)

model_7_9 = train_NNet(train_X_d1_d2, train_y_label_d1_d2)
acc, Y_predicted = predict(test_X_d1_d2, test_y_d1_d2, model_7_9, d1, d2)
acc