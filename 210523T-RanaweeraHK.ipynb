#%% md
# <center>Feedforward and Backpropagation</center>
## <center>Inclass Project 2 - MA4144</center>

This project contains 12 tasks/questions to be completed, some require written answers. Questions that required written answers are given in blue fonts. Some written questions are open ended, they do not have a correct or wrong answer. You are free to give your opinions, but please provide related answers within the context.

After finishing project run the entire notebook once and **save the notebook as a pdf** (File menu -> Save and Export Notebook As -> PDF). You are **required to upload this PDF on moodle and also the ipynb notebook file as well**.

***
#%% md
## Outline of the project

The aim of the project is to build a Multi Layer perceptron (MLP) model from scratch for binary classification. That is given an input $x$ output the associated class label $0$ or $1$.

In particular, we will classify images of handwritten digits ($0, 1, 2, \cdots, 9$). For example, given a set of handwritten digit images that only contain two digits (Eg: $1$ and $5$) the model will classify the images based on the written digit.

For this we will use the MNIST dataset (collection of $28 \times 28$ images of handwritten digits) - you can find additional information about MNIST [here](https://en.wikipedia.org/wiki/MNIST_database).

<img src="https://upload.wikimedia.org/wikipedia/commons/f/f7/MnistExamplesModified.png" width="250">

***

#%% md
Use the below cell to use any include any imports
#%%
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score
#%% md
## Section 1: Preparing the data
#%%
#Load the dataset as training and testing, then print out the shapes of the data matrices.
#The training data will be provided to you.

data = np.load('train_mnist.npz')
train_X = data['x']
train_y = data['y']
print(train_X.shape)
print(train_y.shape)
#%% md
**Q1.** In the following cell write code to display $5$ random images in train_X and it's corresponding label in train_y. Each time it is run, you should get a different set of images. The imshow function in the matplotlib library could be useful. Display them as [grayscale images](https://en.wikipedia.org/wiki/Grayscale).
#%%
#TODO Code to display 5 random handritten images from train_X and corresponting labels from train_y

num_samples = 5
random_indices = np.random.choice(train_X.shape[0], num_samples, replace=False)

plt.figure(figsize=(12, 3))
for i, idx in enumerate(random_indices):
    plt.subplot(1, num_samples, i + 1)
    plt.imshow(train_X[idx], cmap="gray")  # show as grayscale
    plt.title(f"Label: {train_y[idx]}")
    plt.axis("off")
plt.show()
#%% md
**Q2.** Given two digits $d_1$ and $d_2$, both between $0$ and $9$, in the following cell fill in the function body to extract all the samples corresponding to $d_1$ or $d_2$ only, from the dataset $X$ and labels $y$. You can use the labels $y$ to filter the dataset. Assume that the label for the $i$th image $X[i]$ in $X$ is given by $y[i]$. The function should return the extracted samples $X_{extracted}$ and corresponding labels $y_{extracted}$. Avoid using for loops as much as possible, infact you do not need any for loops. numpy.where function should be useful.
#%%
def extract_digits(X, y, d1, d2):

    assert d1 in range(0, 10), "d1 should be a number between 0 and 9 inclusive"
    assert d2 in range(0, 10), "d2 should be a number between 0 and 9 inclusive"

    # Create a boolean mask where y equals d1 or d2
    mask = (y == d1) | (y == d2)

    # Apply mask to extract samples and labels
    X_extracted = X[mask]
    y_extracted = y[mask]

    return (X_extracted, y_extracted)
#%% md
**Q3.** Both the training dataset is a 3 dimensional numpy array, each image occupies 2 dimensions. For convenience of processing data we usually comvert each $28 \times 28$ image matrix to a vector with $784$ entries. We call this process **vectorize images**.

Once we vectorize the images, the vectorized data set would be structured as follows: $i$th row will correspond to a single image and $j$th column will correspond to the $j$th pixel value of each vectorized image. However going along with the convention we discussed in the lecture, the input to the MLP model will require that the columns correspond to individual images. Hence we also require a transpose of the vectorized results.

The pixel values in the images will range from $0$ to $255$. Normalize the pixel values between $0$ and $1$, by dividing each pixel value of each image by the maximum pixel value of that image. Simply divide each column of the resulting matrix above by the max of each column. 

<center><img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTdN_8m9FEqjqAB07obTmB6gNc7S2rSoGBYaA&s"></center>

Given a dataset $X$ of size $N \times 28 \times 28$, in the following cell fill in the function to do the following in order;
1. Vectorize the dataset resulting in dataset of size $N \times 784$.
2. Transpose the vectorized result.
3. Normalize the pixel values of each image.
4. Finally return the vectorized, transposed and normalized dataset $X_{transformed}$.

Again, avoid for loops, functions such as numpy.reshape, numpy.max etc should be useful.
#%%
def vectorize_images(X):
    # Step 1: Vectorize (N, 28, 28) → (N, 784)
    X_flat = X.reshape(X.shape[0], -1)

    # Step 2: Transpose → (784, N)
    X_transposed = X_flat.T

    # Step 3: Normalize each column by its max value
    # Add small epsilon to avoid divide-by-zero
    max_per_column = X_transposed.max(axis=0, keepdims=True)
    X_vectorized = X_transposed / (max_per_column + 1e-8)

    return(X_vectorized)
#%% md
**Q4.** In the following cell write code to;

1. Extract images of the digits $d_1 = 1$ and $d_2 = 5$ with their corresponding labels for the training set (train_X, train_y).
2. Then vectorize the data, tranpose the result and normlize the images.
3. Store the results after the final transformations in numpy arrays train_X_1_5, train_y_1_5.
4. Our MLP will output only class labels $0$ and $1$ (not $1$ and $5$), so create numpy arrays to store the class labels as follows:
   $d_1 = 1$ -> class label = 0 and $d_2 = 5$ -> class label = 1. Store them in an array named train_y_1_5.

Use the above functions you implemented above to complete this task. In addtion, numpy.where could be useful. Avoid for loops as much as possible.
#%%
#TODO
#Extract and organize the training data as described above.
#Here you will be using the functions you implemented above appropriately

# Step 1: Extract digits 1 and 5
X_1_5, y_1_5 = extract_digits(train_X, train_y, 1, 5)

# Step 2: Vectorize, transpose, and normalize
train_X_1_5 = vectorize_images(X_1_5)

# Step 3: Remap labels (1 -> 0, 5 -> 1)
train_y_1_5 = np.where(y_1_5 == 1, 0, 1)

print("train_X_1_5 shape:", train_X_1_5.shape)
print("train_y_1_5 shape:", train_y_1_5.shape)
print("Unique labels in train_y_1_5:", np.unique(train_y_1_5))



#%% md
## Section 2: Implementing MLP from scratch with training algorithms.
#%% md
Now we will implement code to build a customizable MLP model. The hidden layers will have the **Relu activation function** and the final output layer will have **Sigmoid activation function**.
#%% md
**Q5.** Recall the following about the activation functions:
1. Sigmoid activation: $y = \sigma(z) = \frac{1}{1 + e^{-z}}$.
2. Derivative of Sigmoid: $y' = \sigma'(z) = \sigma(z) (1 - \sigma(z)) = y(1-y)$
3. ReLu activation: $y = ReLu(z) = max(0, z)$
4. Derivative of ReLu: $y' = ReLu'(z) = \begin{cases} 0 \; \textrm{if } z < 0 \\ 1 \; \textrm{otherwise} \end{cases} = \begin{cases} 0 \; \textrm{if } y = 0 \\ 1 \; \textrm{otherwise} \end{cases}$

In the following cell implement the functions to compute activation functions Sigmoid and ReLu given $z$ and derivatives of the Sigmoid and ReLu activation functions given $y$. Note that, in the implementation, the derivative functions should actually accept $y$ as the input not $z$.

In practice the input will not be just single numbers, but matrices. So functions or derivatives should be applied elementwise on matrices. Again avoid for loops, use the power of numpy arrays - search for numpy's capability of doing elementwise computations.

Important: When implementing the sigmoid function make sure you handle overflows due to $e^{-z}$ being too large. To avoid you can choose to set the sigmoid value to 'the certain appropriate value' if $z$ is less than a certain good enough negative threshold. If you do not handle overflows, the entire result will be useless since the MLP will just output Nan (not a number) for every input at the end.
#%%
def sigmoid(Z):
    # Clip Z to prevent overflow in exp
    Z_clipped = np.clip(Z, -500, 500)
    sigma = 1 / (1 + np.exp(-Z_clipped))
    return(sigma)

def deriv_sigmoid(Y):
    # derivative of sigmoid given output Y
    sigma_prime = Y * (1 - Y)
    return(sigma_prime)


def ReLu(Z):
    relu = np.maximum(0, Z)
    return(relu)

def deriv_ReLu(Y):
    # derivative of ReLU given output Y
    relu_prime = np.where(Y > 0, 1, 0)
    return(relu_prime)

#%% md
**Q6.** The following piece of code defines a simple MLP architecture as a Python class and subsequent initialization of a MLP model. <font color='blue'>Certain lines of code contains commented line numbers. Write a short sentence for each such line explaining its purpose. Feel free to refer to the lecture notes or any resources to answers these question. In addition, explain what the Y, Z, W variables refer to and their purpose</font>
#%%
class NNet:
    def __init__(self, input_size = 784, output_size = 1, batch_size = 1000, hidden_layers = [500, 250, 50]):
        self.Y = []
        self.Z = []
        self.W = []
        self.input_size = input_size
        self.output_size = output_size
        self.batch_size = batch_size
        self.hidden_layers = hidden_layers

        layers = [input_size] + hidden_layers + [output_size]
        L = len(hidden_layers) + 1
    
        for i in range(1, L + 1):
            self.Y.append(np.zeros((layers[i], batch_size)))                        #line1
            self.Z.append(np.zeros((layers[i], batch_size)))                        #Line2
            self.W.append(2*(np.random.rand(layers[i], layers[i-1] + 1) - 0.5))     #Line3
#%% md
**Questions** (write answers in the cell below as strings)

(i) What does the Y, Z, W variables refer to and their purpose?

(ii) Line1: Explanation

(iii) Line2: Explanation

(iv) Line3: Explanation
#%%
#Write answers here as strings:

ans_i = "Y stores the activations (outputs) of each layer after applying the activation function, Z stores the pre-activation values (weighted sums before activation), and W stores the weight matrices (including bias) that connect one layer to the next. Together they represent the forward propagation flow and trainable parameters of the MLP."

ans_ii = "Line1 initializes the activations Y for layer i as a zero matrix of shape (neurons_in_layer_i, batch_size). This will later hold the output values of the neurons after activation."

ans_iii = "Line2 initializes the pre-activation values Z for layer i as a zero matrix of the same shape. This will later hold the weighted sums W·X + b before applying the activation function."

ans_iv = "Line3 initializes the weight matrix W for layer i with random values in the range [-1, 1). Its shape is (neurons_in_layer_i, neurons_in_previous_layer + 1), where the +1 accounts for the bias term."

#%% md
**Q7.** Now we will implement the feedforward algorithm. Recall from the lectures that for each layer $l$ there is input $Y^{(l-1)}$ from the previous layer if $l > 1$ and input data $X$ if $l = 1$. Then we compute $Z^{(l)}$ using the weight matrix $W^{(l)}$ as follows from matrix multiplication:

$Z^{(l)} = W^{(l)} Y^{(l-1)}$

Make sure that during multiplication you add an additional row of one's to $Y^{(l-1)}$ to accommodate the bias term (concatenate the row of ones as the last row to be consistent with the grader). However, the rows of ones should not permanently remain on $Y^{(l-1)}$. <font color='blue'>Explain what the bias term is and how adding a row of one's help with the bias terms.</font> The weight matrices are initialised to afford this extra bias term, so no change to either $Z^{(l)}$ or $W^{(l)}$ is needed.

Next compute $Y^{(l)}$, the output of layer $l$ by activation through sigmoid.

$Y^{(l)} = \sigma(Z^{(l)})$

The implemented feedforward algorithm should take in a NNet model and an input matrix $X$ and output the modified MLP model - the $Y$'s and $Z$'s computed should be stored in the model for the backpropagation algorithm.

As usual, avoid for loops as much as possible, use the power of numpy. However, you may use a for loop to iterate through the layers of the model.
#%%
def feedforward(model, X):
    # Input to the first layer is just the data X
    Y_prev = X

    for l in range(len(model.W)):
        # Add a row of ones at the bottom of Y_prev (for bias handling)
        Y_with_bias = np.vstack([Y_prev, np.ones((1, Y_prev.shape[1]))])

        # Compute pre-activation Z^(l) = W^(l) * Y_with_bias
        model.Z[l] = np.dot(model.W[l], Y_with_bias)

        # Compute activation Y^(l)
        # Use ReLU for hidden layers, Sigmoid for output layer
        if l == len(model.W) - 1:  # Output layer
            model.Y[l] = sigmoid(model.Z[l])
        else:  # Hidden layers
            model.Y[l] = ReLu(model.Z[l])

        # Update Y_prev for the next layer
        Y_prev = model.Y[l]

    return(model)
#%% md
**Question** (write answer in the cell below as a string)

Explain what the bias term is and how adding a row of one's help with the bias terms.
#%%
#Write the answer here as a string:

ans_bias = "The bias term in a neural network allows each neuron to shift its activation function left or right, giving the model more flexibility to fit the data. Adding a row of ones to the input matrix allows the bias to be incorporated directly into the matrix multiplication with the weight matrix, since the weight matrix includes an extra column for the bias. This way, the bias is automatically applied without needing a separate addition step."
#%% md
**Q8.** Now we will implement the backpropagation algorithm. The cost function $C$ at the end is given by the square loss.

$C = \frac{1}{2} ||Y^{(L)} - Y||^{2}$, where $Y^{(L)}$ is the final output vector of the feedforward algorithm and $Y$ is the actual label vector associated with the input $X$.

At each layer $l = 1, 2, \cdots, L$ we compute the following (note that the gradients are matrices with the same dimensions as the variable to which we derivating with respect to):

1. Gradient of $C$ with respect to $Z^{(l)}$ as <br> $\frac{\partial C}{\partial Z^{(l)}} = deriv(A^{(l)}(Z^{(l)})) \odot \frac{\partial C}{\partial Y^{(L)}} $, <br> where $A^{(l)}$ is the activation function of the $l$th layer, and we use the derivative of that here. The $\odot$ refers to the elementwise multiplication.

2. Gradient of $C$ with respect to $W^{(l)}$ as <br> $\frac{\partial C}{\partial W^{(l)}} = \frac{\partial C}{\partial Z^{(l)}} (Y^{(l-1)})^{T}$ <br> this is entirely matrix multiplication.

3. Gradient of $C$ with respect to $Y^{(l-1)}$ as <br> $\frac{\partial C}{\partial Y^{(l-1)}} = (W^{(l)})^{T} \frac{\partial C}{\partial Z^{(l)}}$ <br> this is also entirely matrix multiplication.

4. Update weights by: <br> $W^{(l)} \leftarrow W^{(l)} - \eta \frac{\partial C}{\partial W^{(l)}}$, <br> where $\eta > 0$ is the learning rate.

The loss derivative (the gradient of $C$ with respect to $Y^{(L)}$) at the last layer is given by:

$\frac{\partial C}{\partial Y^{(L)}} = Y^{(L)} - Y$

By convention we consider $Y^{(0)} = X$, the input data.

Based on the backpropagation algorithm implement the backpropagation method in the following cell. Remember to temporarily add a row of ones to $Y^{(l-1)}$ (as the last row to be consistent with the grader) when computing $\frac{\partial C}{\partial W^{(l)}}$ as we discussed back in the feedforward algorithm. Make sure you avoid for loops as much as possible.

The function takes in a NNet model, input data $X$ and the corresponding class labels $Y$. learning rate can be set as desired.
#%%
def backpropagation(model, X, Y, eta = 0.01):
    L = len(model.W)  # number of layers
    batch_size = X.shape[1]

    # Step 1: Compute derivative of loss w.r.t final output
    dC_dY = model.Y[-1] - Y  # shape: (output_size, batch_size)

    # Step 2: Backpropagate through layers
    delta = dC_dY.copy()  # initialize delta for last layer

    for l in reversed(range(L)):
        # Compute derivative of activation function
        # Use correct derivative for each layer type
        if l == L - 1:  # Output layer (Sigmoid)
            dA = deriv_sigmoid(model.Y[l])
        else:  # Hidden layers (ReLU)
            dA = deriv_ReLu(model.Y[l])

        # Gradient w.r.t Z^(l)
        dC_dZ = delta * dA  # elementwise multiplication

        # Input to current layer
        if l == 0:
            Y_prev = X
        else:
            Y_prev = model.Y[l-1]

        # Add bias row temporarily for weight gradient
        Y_with_bias = np.vstack([Y_prev, np.ones((1, batch_size))])

        # Gradient w.r.t weights
        dC_dW = np.dot(dC_dZ, Y_with_bias.T) / batch_size  # average over batch

        # Update weights
        model.W[l] -= eta * dC_dW

        # Compute delta for next layer (if any)
        W_no_bias = model.W[l][:, :-1]  # remove bias column
        delta = np.dot(W_no_bias.T, dC_dZ)

    return(model)
#%% md
**Q9.** Now implement the training algorithm.

The training method takes in training data $X$, actual label $Y$, number of epochs, batch_size, learning rate $\eta > 0$. The training will happen in epochs. For each epoch, permute the data columns of both $X$ and $Y$, then divide both $X$ and $Y$ into mini batches each with the given batch size. Then run the feedforward and backpropagation for each such batch iteratively.

At the end of each iteration, keep trach of the cost $C$ and the $l_2$-norm of change in each weight matrix $W^{(l)}$.

At the end of the last epoch, plot the variation cost $C$ and change in weight matrices. Then return the trained model.


#%%
def train_NNet(X, Y, epochs = 20, batch_size = 1000, eta = 0.01):
    """
    Train the NNet model on input data X and labels Y using mini-batch SGD.

    Parameters:
        X: input data matrix (input_size x N)
        Y: true labels (1 x N)
        epochs: number of training epochs
        batch_size: size of each mini-batch
        eta: learning rate
    """
    # Initialize model
    input_size = X.shape[0]
    output_size = Y.shape[0]
    model = NNet(input_size=input_size, output_size=output_size, batch_size=batch_size)

    N = X.shape[1]  # total number of samples

    # Lists to track cost and weight changes
    cost_history = []
    weight_change_history = [[] for _ in range(len(model.W))]

    for epoch in range(epochs):
        # Step 1: Shuffle data
        perm = np.random.permutation(N)
        X_shuffled = X[:, perm]
        Y_shuffled = Y[:, perm]

        # Step 2: Mini-batch iteration
        for start in range(0, N, batch_size):
            end = min(start + batch_size, N)
            X_batch = X_shuffled[:, start:end]
            Y_batch = Y_shuffled[:, start:end]

            # Step 3: Store old weights to compute change
            old_weights = [W.copy() for W in model.W]

            # Step 4: Feedforward
            model.batch_size = X_batch.shape[1]  # update batch size
            model = feedforward(model, X_batch)

            # Step 5: Backpropagation
            model = backpropagation(model, X_batch, Y_batch, eta)

            # Step 6: Compute cost (square loss)
            Y_pred = model.Y[-1]
            cost = 0.5 * np.mean((Y_pred - Y_batch)**2)
            cost_history.append(cost)

            # Step 7: Compute L2-norm of weight changes
            for l in range(len(model.W)):
                delta_W = model.W[l] - old_weights[l]
                weight_change_history[l].append(np.linalg.norm(delta_W))

        print(f"Epoch {epoch+1}/{epochs}, Cost: {cost:.6f}")

    # Step 8: Plot cost and weight changes
    plt.figure(figsize=(12,5))
    plt.subplot(1,2,1)
    plt.plot(cost_history, label="Cost")
    plt.xlabel("Iteration")
    plt.ylabel("Cost")
    plt.title("Cost over iterations")
    plt.legend()

    plt.subplot(1,2,2)
    for l in range(len(model.W)):
        plt.plot(weight_change_history[l], label=f"Layer {l+1}")
    plt.xlabel("Iteration")
    plt.ylabel("L2 change in W")
    plt.title("Weight change per layer")
    plt.legend()
    plt.show()

    return(model)
#%% md
## Section 3: Evaluation using test data
#%% md
**Q10.** Implement the following function predict. Given test images (3 dimensional numpy array that contains $28 \times 28$ digit images) it will correctly recognize the written digits between d1 and d2. You can assume that test_images will only contain images of digits d1 and d2. Inside predict you would need to preprocess the images using vectorize, predict the correct labels using model and then output the correct lables. Output should be a vector predicted of d1's and d2's, predicted[i] should correspond to the test_image[i].
#%%
def predict(model, test_images, d1, d2):
    # Step 1: Vectorize, transpose, and normalize test images
    X_test = vectorize_images(test_images)  # shape: (784, N)

    # Step 2: Set batch size in model to match test data
    model.batch_size = X_test.shape[1]

    # Step 3: Feedforward through the trained model
    model = feedforward(model, X_test)

    # Step 4: Get the output layer predictions
    Y_out = model.Y[-1]  # shape: (1, N)

    # Step 5: Convert outputs to class labels 0 or 1
    # Threshold 0.5 for sigmoid
    class_labels = (Y_out > 0.5).astype(int).flatten()

    # Step 6: Map back to original digits
    predicted = np.where(class_labels == 0, d1, d2)

    return(predicted)
#%% md
**Q11.** Use the train_NNet function to train a MLP model to classify between images of digits $1$ and $5$. An accuracy $>= 99%$ is achievable. Test with different batch sizes, $\eta$ values and hidden layers. Find which of those hyperparameters gives the best test accuracy. Name this model, model_1_5. This model will be tested on unseen test data within the grader. So make sure you train the best possible model. The grader will use your own predict function to evaluate the model.
#%%
# Train model for digits 1 and 5
print("Training model for digits 1 and 5...")

# Prepare data - convert train_y_1_5 to row vector for training
y_train_row = train_y_1_5.reshape(1, -1)

# Train with optimized hyperparameters for better accuracy
model_1_5 = train_NNet(train_X_1_5, y_train_row, epochs=50, batch_size=200, eta=0.01)

print("Training completed for digits 1 and 5!")

# Evaluate accuracy on training data
print("\n=== Accuracy Evaluation for Digits 1 and 5 ===")

# Convert training data back to image format for prediction
X_train_images = train_X_1_5.T.reshape(-1, 28, 28)  # Convert back to (N, 28, 28)
y_pred_train = predict(model_1_5, X_train_images, 1, 5)

# Calculate training accuracy
train_accuracy = np.mean(y_pred_train == (train_y_1_5 * 4 + 1)) * 100  # Convert 0,1 back to 1,5
print(f"Training Accuracy: {train_accuracy:.2f}%")

# Also test with a subset for validation
from sklearn.model_selection import train_test_split
X_subset, X_val, y_subset, y_val = train_test_split(
    X_train_images, train_y_1_5 * 4 + 1, test_size=0.2, random_state=42  # Convert back to 1,5
)

y_pred_val = predict(model_1_5, X_val, 1, 5)
val_accuracy = np.mean(y_pred_val == y_val) * 100
print(f"Validation Accuracy: {val_accuracy:.2f}%")
#%% md
**Q12.** Do the same as in Q11 with the digits $7$ and $9$ Name this model, model_7_9. This model will be tested on unseen test data within the grader. So make sure you train the best possible model. The grader will use your own predict function to evaluate the model.
#%%
# Train model for digits 7 and 9
print("Training model for digits 7 and 9...")

# Step 1: Extract digits 7 and 9 from training data
train_X_7_9_raw, train_y_7_9_raw = extract_digits(train_X, train_y, 7, 9)

# Step 2: Vectorize, transpose, normalize
train_X_7_9 = vectorize_images(train_X_7_9_raw)

# Step 3: Convert labels to 0/1: 7 -> 0, 9 -> 1
train_y_7_9 = np.where(train_y_7_9_raw == 7, 0, 1)

# Step 4: Convert to row vector for training
y_train_row = train_y_7_9.reshape(1, -1)

# Step 5: Train with good hyperparameters
model_7_9 = train_NNet(train_X_7_9, y_train_row, epochs=30, batch_size=500, eta=0.05)

print("Training completed for digits 7 and 9!")

# Evaluate accuracy on training data
print("\n=== Accuracy Evaluation for Digits 7 and 9 ===")

# Convert training data back to image format for prediction
X_train_images = train_X_7_9.T.reshape(-1, 28, 28)  # Convert back to (N, 28, 28)
y_pred_train = predict(model_7_9, X_train_images, 7, 9)

# Calculate training accuracy
original_labels = np.where(train_y_7_9 == 0, 7, 9)  # Convert 0,1 back to 7,9
train_accuracy = np.mean(y_pred_train == original_labels) * 100
print(f"Training Accuracy: {train_accuracy:.2f}%")

# Also test with a subset for validation
from sklearn.model_selection import train_test_split
X_subset, X_val, y_subset, y_val = train_test_split(
    X_train_images, original_labels, test_size=0.2, random_state=42
)

y_pred_val = predict(model_7_9, X_val, 7, 9)
val_accuracy = np.mean(y_pred_val == y_val) * 100
print(f"Validation Accuracy: {val_accuracy:.2f}%")
#%%
