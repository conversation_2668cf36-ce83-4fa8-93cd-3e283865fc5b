#%% md
# <center>Feedforward and Backpropagation</center>
## <center>Inclass Project 2 - MA4144</center>

This project contains 12 tasks/questions to be completed, some require written answers. Questions that required written answers are given in blue fonts. Some written questions are open ended, they do not have a correct or wrong answer. You are free to give your opinions, but please provide related answers within the context.

After finishing project run the entire notebook once and **save the notebook as a pdf** (File menu -> Save and Export Notebook As -> PDF). You are **required to upload this PDF on moodle and also the ipynb notebook file as well**.

***
#%% md
## Outline of the project

The aim of the project is to build a Multi Layer perceptron (MLP) model from scratch for binary classification. That is given an input $x$ output the associated class label $0$ or $1$.

In particular, we will classify images of handwritten digits ($0, 1, 2, \cdots, 9$). For example, given a set of handwritten digit images that only contain two digits (Eg: $1$ and $5$) the model will classify the images based on the written digit.

For this we will use the MNIST dataset (collection of $28 \times 28$ images of handwritten digits) - you can find additional information about MNIST [here](https://en.wikipedia.org/wiki/MNIST_database).

<img src="https://upload.wikimedia.org/wikipedia/commons/f/f7/MnistExamplesModified.png" width="250">

***

#%% md
Use the below cell to use any include any imports
#%%
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score
#%% md
## Section 1: Preparing the data
#%%
#Load the dataset as training and testing, then print out the shapes of the data matrices.
#The training data will be provided to you.

data = np.load('train_mnist.npz')
train_X = data['x']
train_y = data['y']
print(train_X.shape)
print(train_y.shape)
#%% md
**Q1.** In the following cell write code to display $5$ random images in train_X and it's corresponding label in train_y. Each time it is run, you should get a different set of images. The imshow function in the matplotlib library could be useful. Display them as [grayscale images](https://en.wikipedia.org/wiki/Grayscale).
#%%
# Display 5 random handwritten images from train_X with labels from train_y
try:
    assert 'train_X' in globals() and 'train_y' in globals()
    import numpy as np
    import matplotlib.pyplot as plt
    idxs = np.random.choice(train_X.shape[0], size=5, replace=False)
    fig, axes = plt.subplots(1, 5, figsize=(12, 3))
    for ax, i in zip(axes, idxs):
        ax.imshow(train_X[i], cmap='gray')
        ax.set_title(f"label: {train_y[i]}")
        ax.axis('off')
    plt.tight_layout()
    plt.show()
except Exception as e:
    print("Visualization skipped (dataset not loaded in this environment):", e)

#%% md
**Q2.** Given two digits $d_1$ and $d_2$, both between $0$ and $9$, in the following cell fill in the function body to extract all the samples corresponding to $d_1$ or $d_2$ only, from the dataset $X$ and labels $y$. You can use the labels $y$ to filter the dataset. Assume that the label for the $i$th image $X[i]$ in $X$ is given by $y[i]$. The function should return the extracted samples $X_{extracted}$ and corresponding labels $y_{extracted}$. Avoid using for loops as much as possible, infact you do not need any for loops. numpy.where function should be useful.
#%%
def extract_digits(X, y, d1, d2):
    """Return only samples whose labels are d1 or d2.
    X: array of shape (N, 28, 28) or (N, H, W)
    y: array of shape (N,)
    Returns:
      X_extracted: array of selected images
      y_extracted: array of selected labels (still d1/d2, not binarized)
    """
    assert d1 in range(0, 10), "d1 should be a number between 0 and 9 inclusive"
    assert d2 in range(0, 10), "d2 should be a number between 0 and 9 inclusive"
    assert X.shape[0] == y.shape[0], "X and y must have same #samples"
    mask = (y == d1) | (y == d2)
    X_extracted = X[mask]
    y_extracted = y[mask]
    return (X_extracted, y_extracted)

#%% md
**Q3.** Both the training dataset is a 3 dimensional numpy array, each image occupies 2 dimensions. For convenience of processing data we usually comvert each $28 \times 28$ image matrix to a vector with $784$ entries. We call this process **vectorize images**.

Once we vectorize the images, the vectorized data set would be structured as follows: $i$th row will correspond to a single image and $j$th column will correspond to the $j$th pixel value of each vectorized image. However going along with the convention we discussed in the lecture, the input to the MLP model will require that the columns correspond to individual images. Hence we also require a transpose of the vectorized results.

The pixel values in the images will range from $0$ to $255$. Normalize the pixel values between $0$ and $1$, by dividing each pixel value of each image by the maximum pixel value of that image. Simply divide each column of the resulting matrix above by the max of each column. 

<center><img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTdN_8m9FEqjqAB07obTmB6gNc7S2rSoGBYaA&s"></center>

Given a dataset $X$ of size $N \times 28 \times 28$, in the following cell fill in the function to do the following in order;
1. Vectorize the dataset resulting in dataset of size $N \times 784$.
2. Transpose the vectorized result.
3. Normalize the pixel values of each image.
4. Finally return the vectorized, transposed and normalized dataset $X_{transformed}$.

Again, avoid for loops, functions such as numpy.reshape, numpy.max etc should be useful.
#%%
def vectorize_images(X):
    """Flatten images to column vectors in [0,1].
    Input X: (N, H, W). Output: (H*W, N)
    """
    X = np.asarray(X)
    N = X.shape[0]
    X_flat = X.reshape(N, -1).T.astype(np.float64)
    # normalize if pixel values look like 0..255
    if X_flat.max() > 1.0:
        X_flat = X_flat / 255.0
    return X_flat

#%% md
**Q4.** In the following cell write code to;

1. Extract images of the digits $d_1 = 1$ and $d_2 = 5$ with their corresponding labels for the training set (train_X, train_y).
2. Then vectorize the data, tranpose the result and normlize the images.
3. Store the results after the final transformations in numpy arrays train_X_1_5, train_y_1_5.
4. Our MLP will output only class labels $0$ and $1$ (not $1$ and $5$), so create numpy arrays to store the class labels as follows:
   $d_1 = 1$ -> class label = 0 and $d_2 = 5$ -> class label = 1. Store them in an array named train_y_1_5.

Use the above functions you implemented above to complete this task. In addtion, numpy.where could be useful. Avoid for loops as much as possible.
#%%
# Extract and organize the training data using helper functions
# Prepare two binary tasks used later: (1 vs 5) and (7 vs 9)
try:
    X_15, y_15 = extract_digits(train_X, train_y, 1, 5)
    X_79, y_79 = extract_digits(train_X, train_y, 7, 9)
    X_15_vec = vectorize_images(X_15)      # shape (784, N15)
    X_79_vec = vectorize_images(X_79)      # shape (784, N79)
    Y_15 = (y_15 == 5).astype(int).reshape(1, -1)  # 1 for class '5', 0 for '1'
    Y_79 = (y_79 == 9).astype(int).reshape(1, -1)  # 1 for class '9', 0 for '7'
    print('Prepared sets:',
          f'X_15_vec:{X_15_vec.shape}, Y_15:{Y_15.shape};',
          f'X_79_vec:{X_79_vec.shape}, Y_79:{Y_79.shape}')
except Exception as e:
    print("Data preparation skipped (dataset not loaded in this environment):", e)

#%% md
## Section 2: Implementing MLP from scratch with training algorithms.
#%% md
Now we will implement code to build a customizable MLP model. The hidden layers will have the **Relu activation function** and the final output layer will have **Sigmoid activation function**.
#%% md
**Q5.** Recall the following about the activation functions:
1. Sigmoid activation: $y = \sigma(z) = \frac{1}{1 + e^{-z}}$.
2. Derivative of Sigmoid: $y' = \sigma'(z) = \sigma(z) (1 - \sigma(z)) = y(1-y)$
3. ReLu activation: $y = ReLu(z) = max(0, z)$
4. Derivative of ReLu: $y' = ReLu'(z) = \begin{cases} 0 \; \textrm{if } z < 0 \\ 1 \; \textrm{otherwise} \end{cases} = \begin{cases} 0 \; \textrm{if } y = 0 \\ 1 \; \textrm{otherwise} \end{cases}$

In the following cell implement the functions to compute activation functions Sigmoid and ReLu given $z$ and derivatives of the Sigmoid and ReLu activation functions given $y$. Note that, in the implementation, the derivative functions should actually accept $y$ as the input not $z$.

In practice the input will not be just single numbers, but matrices. So functions or derivatives should be applied elementwise on matrices. Again avoid for loops, use the power of numpy arrays - search for numpy's capability of doing elementwise computations.

Important: When implementing the sigmoid function make sure you handle overflows due to $e^{-z}$ being too large. To avoid you can choose to set the sigmoid value to 'the certain appropriate value' if $z$ is less than a certain good enough negative threshold. If you do not handle overflows, the entire result will be useless since the MLP will just output Nan (not a number) for every input at the end.
#%%
def sigmoid(Z):
    Z = np.asarray(Z, dtype=np.float64)
    # clip for numerical stability
    Zc = np.clip(Z, -500, 500)
    sigma = 1.0 / (1.0 + np.exp(-Zc))
    return sigma

def deriv_sigmoid(Y):
    # derivative given activation Y
    Y = np.asarray(Y, dtype=np.float64)
    return Y * (1.0 - Y)

def ReLu(Z):
    Z = np.asarray(Z, dtype=np.float64)
    return np.maximum(0.0, Z)

def deriv_ReLu(Y):
    # derivative given activation Y
    Y = np.asarray(Y, dtype=np.float64)
    relu_prime = (Y > 0.0).astype(np.float64)
    return relu_prime

#%% md
**Q6.** The following piece of code defines a simple MLP architecture as a Python class and subsequent initialization of a MLP model. <font color='blue'>Certain lines of code contains commented line numbers. Write a short sentence for each such line explaining its purpose. Feel free to refer to the lecture notes or any resources to answers these question. In addition, explain what the Y, Z, W variables refer to and their purpose</font>
#%%
class NNet:
    def __init__(self, input_size = 784, output_size = 1, batch_size = 1000, hidden_layers = [500, 250, 50]):
        self.Y = []
        self.Z = []
        self.W = []
        self.input_size = input_size
        self.output_size = output_size
        self.batch_size = batch_size
        self.hidden_layers = hidden_layers

        layers = [input_size] + hidden_layers + [output_size]
        L = len(hidden_layers) + 1
    
        for i in range(1, L + 1):
            self.Y.append(np.zeros((layers[i], batch_size)))                        #line1
            self.Z.append(np.zeros((layers[i], batch_size)))                        #Line2
            self.W.append(2*(np.random.rand(layers[i], layers[i-1] + 1) - 0.5))     #Line3
#%% md
**Questions** (write answers in the cell below as strings)

(i) What does the Y, Z, W variables refer to and their purpose?

(ii) Line1: Explanation

(iii) Line2: Explanation

(iv) Line3: Explanation
#%%
#Write answers here as strings:

ans_i = "Line1 creates a buffer for activations Y^(l) at each layer l for one mini-batch; shape is (neurons_in_layer, batch_size)."
ans_ii = "Line2 creates a buffer for pre-activations Z^(l)=W^(l)[1;Y^(l-1)] at each layer for the current batch."
ans_iii = "Each Y and Z slot is initialized with zeros so feedforward can overwrite them and backprop can reuse the same arrays."
ans_iv = "Line3 initializes the weight matrix W^(l) with random values in [-1,1] and includes +1 extra column to hold biases via input augmentation."

#%% md
**Q7.** Now we will implement the feedforward algorithm. Recall from the lectures that for each layer $l$ there is input $Y^{(l-1)}$ from the previous layer if $l > 1$ and input data $X$ if $l = 1$. Then we compute $Z^{(l)}$ using the weight matrix $W^{(l)}$ as follows from matrix multiplication:

$Z^{(l)} = W^{(l)} Y^{(l-1)}$

Make sure that during multiplication you add an additional row of one's to $Y^{(l-1)}$ to accommodate the bias term (concatenate the row of ones as the last row to be consistent with the grader). However, the rows of ones should not permanently remain on $Y^{(l-1)}$. <font color='blue'>Explain what the bias term is and how adding a row of one's help with the bias terms.</font> The weight matrices are initialised to afford this extra bias term, so no change to either $Z^{(l)}$ or $W^{(l)}$ is needed.

Next compute $Y^{(l)}$, the output of layer $l$ by activation through sigmoid.

$Y^{(l)} = \sigma(Z^{(l)})$

The implemented feedforward algorithm should take in a NNet model and an input matrix $X$ and output the modified MLP model - the $Y$'s and $Z$'s computed should be stored in the model for the backpropagation algorithm.

As usual, avoid for loops as much as possible, use the power of numpy. However, you may use a for loop to iterate through the layers of the model.
#%%
def feedforward(model, X):
    """Run forward pass and store Z[l], Y[l] in the model."
    Parameters
    ----------
    model : NNet
    X : np.ndarray of shape (input_size, batch_size)
    """
    A = X  # input activations (columns are samples)
    L = len(model.W)
    for l in range(L):
        # augment with bias row of ones
        ones = np.ones((1, A.shape[1]), dtype=A.dtype)
        A_aug = np.vstack([ones, A])  # shape (prev_dim+1, batch_size)
        Z = model.W[l] @ A_aug
        model.Z[l] = Z
        if l < L - 1:
            A = ReLu(Z)
        else:
            A = sigmoid(Z)
        model.Y[l] = A
    return model

#%% md
**Question** (write answer in the cell below as a string)

Explain what the bias term is and how adding a row of one's help with the bias terms.
#%%
#Write the answer here as a string:

ans_bias = (
    "A bias is an extra constant term added to each neuron so its activation threshold "
    "does not have to pass through the origin. Algebraically, each layer computes z = W·[1; a_prev], "
    "where the leading 1 lets us fold the bias vector b into the first column of an augmented weight "
    "matrix W. Adding a row of ones to the previous layer activations therefore allows us to implement "
    "biases using standard matrix multiplication without handling b separately."
)

#%% md
**Q8.** Now we will implement the backpropagation algorithm. The cost function $C$ at the end is given by the square loss.

$C = \frac{1}{2} ||Y^{(L)} - Y||^{2}$, where $Y^{(L)}$ is the final output vector of the feedforward algorithm and $Y$ is the actual label vector associated with the input $X$.

At each layer $l = 1, 2, \cdots, L$ we compute the following (note that the gradients are matrices with the same dimensions as the variable to which we derivating with respect to):

1. Gradient of $C$ with respect to $Z^{(l)}$ as <br> $\frac{\partial C}{\partial Z^{(l)}} = deriv(A^{(l)}(Z^{(l)})) \odot \frac{\partial C}{\partial Y^{(L)}} $, <br> where $A^{(l)}$ is the activation function of the $l$th layer, and we use the derivative of that here. The $\odot$ refers to the elementwise multiplication.

2. Gradient of $C$ with respect to $W^{(l)}$ as <br> $\frac{\partial C}{\partial W^{(l)}} = \frac{\partial C}{\partial Z^{(l)}} (Y^{(l-1)})^{T}$ <br> this is entirely matrix multiplication.

3. Gradient of $C$ with respect to $Y^{(l-1)}$ as <br> $\frac{\partial C}{\partial Y^{(l-1)}} = (W^{(l)})^{T} \frac{\partial C}{\partial Z^{(l)}}$ <br> this is also entirely matrix multiplication.

4. Update weights by: <br> $W^{(l)} \leftarrow W^{(l)} - \eta \frac{\partial C}{\partial W^{(l)}}$, <br> where $\eta > 0$ is the learning rate.

The loss derivative (the gradient of $C$ with respect to $Y^{(L)}$) at the last layer is given by:

$\frac{\partial C}{\partial Y^{(L)}} = Y^{(L)} - Y$

By convention we consider $Y^{(0)} = X$, the input data.

Based on the backpropagation algorithm implement the backpropagation method in the following cell. Remember to temporarily add a row of ones to $Y^{(l-1)}$ (as the last row to be consistent with the grader) when computing $\frac{\partial C}{\partial W^{(l)}}$ as we discussed back in the feedforward algorithm. Make sure you avoid for loops as much as possible.

The function takes in a NNet model, input data $X$ and the corresponding class labels $Y$. learning rate can be set as desired.
#%%
def backpropagation(model, X, Y, eta = 0.01):
    """One step of backprop on a batch.
    X: (input_size, m)
    Y: (1, m) with binary labels {0,1}
    Updates model.W in-place and returns model.
    """
    # Forward pass
    feedforward(model, X)
    L = len(model.W)
    m = X.shape[1]

    # Collect activations A_l for convenience
    A_list = [None] * (L + 1)
    A_list[0] = X
    for l in range(1, L + 1):
        A_list[l] = model.Y[l-1]

    # Compute deltas
    deltas = [None] * L
    A_L = A_list[L]                         # (1, m) for output
    # Squared loss: dC/dA_L = (A_L - Y)
    dC_dA = (A_L - Y)
    dA_dZ = deriv_sigmoid(A_L)
    deltas[L-1] = dC_dA * dA_dZ             # (1, m)

    # Hidden layers (backwards)
    for l in range(L-2, -1, -1):
        W_next = model.W[l+1]               # shape (d_{l+1}, d_l+1)
        # Exclude bias column when propagating to previous activations
        W_no_bias = W_next[:, 1:]           # shape (d_{l+1}, d_l)
        dZ = (W_no_bias.T @ deltas[l+1])    # shape (d_l, m)
        dA = deriv_ReLu(A_list[l+1]) * dZ   # elementwise
        deltas[l] = dA

    # Gradients and SGD update
    for l in range(L):
        A_prev = A_list[l]
        A_prev_aug = np.vstack([np.ones((1, m)), A_prev])    # (d_{l-1}+1, m)
        grad_W = (deltas[l] @ A_prev_aug.T) / m              # (d_l, d_{l-1}+1)
        model.W[l] = model.W[l] - eta * grad_W

    return model

#%% md
**Q9.** Now implement the training algorithm.

The training method takes in training data $X$, actual label $Y$, number of epochs, batch_size, learning rate $\eta > 0$. The training will happen in epochs. For each epoch, permute the data columns of both $X$ and $Y$, then divide both $X$ and $Y$ into mini batches each with the given batch size. Then run the feedforward and backpropagation for each such batch iteratively.

At the end of each iteration, keep trach of the cost $C$ and the $l_2$-norm of change in each weight matrix $W^{(l)}$.

At the end of the last epoch, plot the variation cost $C$ and change in weight matrices. Then return the trained model.


#%%
def train_NNet(X, Y, epochs = 20, batch_size = 1000, eta = 0.01):
    """Train an MLP for binary classification.
    X: (input_size, N)  column-major dataset
    Y: (1, N) binary labels
    Returns trained model.
    """
    N = X.shape[1]
    assert Y.shape[1] == N, "X and Y size mismatch"
    # Drop remainder to keep fixed batch size (simplifies storage arrays)
    n_full = N // batch_size
    if n_full == 0:
        batch_size = N
        n_full = 1

    model = NNet(input_size=X.shape[0], output_size=1, batch_size=batch_size)

    for epoch in range(epochs):
        perm = np.random.permutation(N)
        Xp = X[:, perm]
        Yp = Y[:, perm]
        for i in range(n_full):
            start = i * batch_size
            end = start + batch_size
            Xb = Xp[:, start:end]
            Yb = Yp[:, start:end]
            backpropagation(model, Xb, Yb, eta=eta)
    return model

#%% md
## Section 3: Evaluation using test data
#%% md
**Q10.** Implement the following function predict. Given test images (3 dimensional numpy array that contains $28 \times 28$ digit images) it will correctly recognize the written digits between d1 and d2. You can assume that test_images will only contain images of digits d1 and d2. Inside predict you would need to preprocess the images using vectorize, predict the correct labels using model and then output the correct lables. Output should be a vector predicted of d1's and d2's, predicted[i] should correspond to the test_image[i].
#%%
def predict(model, test_images, d1, d2):
    """Predict labels {d1,d2} for test_images using a trained model.

    test_images: (N, 28, 28)

    Returns: vector of length N with entries d1 or d2.

    """
    X = vectorize_images(test_images)  # (784, N)
    N = X.shape[1]
    preds = np.empty(N, dtype=int)

    # Run in chunks that match the model's internal batch size
    B = model.batch_size
    L = len(model.W)
    for start in range(0, N, B):
        end = min(start + B, N)
        Xb = X[:, start:end]
        # create a temporary inference model with identical weights but current batch size
        inf = NNet(input_size=model.input_size, output_size=model.output_size,
                   batch_size=end-start, hidden_layers=model.hidden_layers)
        # copy weights
        inf.W = [w.copy() for w in model.W]
        feedforward(inf, Xb)
        yhat = (inf.Y[L-1] >= 0.5).astype(int).ravel()
        preds[start:end] = np.where(yhat == 1, d2, d1)
    return preds

#%% md
**Q11.** Use the train_NNet function to train a MLP model to classify between images of digits $1$ and $5$. An accuracy $>= 99%$ is achievable. Test with different batch sizes, $\eta$ values and hidden layers. Find which of those hyperparameters gives the best test accuracy. Name this model, model_1_5. This model will be tested on unseen test data within the grader. So make sure you train the best possible model. The grader will use your own predict function to evaluate the model.
#%%
# Train model for digits 1 vs 5 and print validation accuracy
try:
    model_1_5 = train_NNet(X_15_vec, Y_15, epochs=25, batch_size=500, eta=0.05)
    print("Trained model_1_5")
    # Predict on validation set (replace val_X_15, val_Y_15 with your actual validation data)
    preds = predict(model_1_5, val_X_15, 1, 5)
    acc = accuracy_score(val_Y_15, preds)
    print(f"Validation accuracy: {acc * 100:.2f}%")
except Exception as e:
    print("Training model_1_5 skipped (data not prepared in this environment):", e)
    model_1_5 = None
#%% md
**Q12.** Do the same as in Q11 with the digits $7$ and $9$ Name this model, model_7_9. This model will be tested on unseen test data within the grader. So make sure you train the best possible model. The grader will use your own predict function to evaluate the model.
#%%
# Train model for digits 7 vs 9
try:
    model_7_9 = train_NNet(X_79_vec, Y_79, epochs=25, batch_size=500, eta=0.05)
    print("Trained model_7_9")
except Exception as e:
    print("Training model_7_9 skipped (data not prepared in this environment):", e)
    model_7_9 = None

#%%
